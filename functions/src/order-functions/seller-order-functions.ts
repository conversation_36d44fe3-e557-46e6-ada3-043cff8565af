import { onCall, HttpsError } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import {
  requireAuthentication,
  validateOrderCreationParams,
  validateSellerOwnership,
  validatePurchaseParams,
} from "../services/auth-middleware";
import { UserType } from "../types";
import { processSellerPurchase } from "../services/purchase-flow-service";
import { createOrder } from "../services/order-creation-service";

export const createOrderAsSeller = onCall(async (request) => {
  const authRequest = requireAuthentication(request);
  const { sellerId, collectionId, price, owned_gift_id } = request.data;

  validateOrderCreationParams(request.data, UserType.SELLER);
  validateSellerOwnership(authRequest, sellerId);

  try {
    const db = admin.firestore();

    const result = await createOrder(db, {
      userId: sellerId,
      collectionId,
      price,
      owned_gift_id,
      userType: UserType.SELLER,
      secondaryMarketPrice: null,
    });

    return result;
  } catch (error) {
    console.error("Error creating order as seller:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while creating order."
    );
  }
});

export const makePurchaseAsSeller = onCall(async (request) => {
  const authRequest = requireAuthentication(request);
  const { sellerId, orderId } = request.data;

  validatePurchaseParams(request.data, UserType.SELLER);
  validateSellerOwnership(authRequest, sellerId);

  try {
    const db = admin.firestore();

    const result = await processSellerPurchase(db, sellerId, orderId);

    return result;
  } catch (error) {
    console.error("Error making purchase as seller:", error);
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while making purchase."
    );
  }
});
