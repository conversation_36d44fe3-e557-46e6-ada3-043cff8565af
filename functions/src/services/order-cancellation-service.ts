import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus, UserRole } from "../types";
import {
  spendLockedFunds,
  updateUserBalance,
  unlockFunds,
} from "./balance-service";
import {
  getAppConfig,
  applyFeeToMarketplaceRevenue,
  applyFixedCancelOrderFee,
} from "./fee-service";
import { BPS_DIVISOR } from "../constants";
import { safeMultiply, safeDivide, safeSubtract } from "../utils";

export interface CancellationResult {
  success: boolean;
  message: string;
  feeApplied?: number;
  feeType?: "fixed" | "dynamic";
}

export async function processOrderCancellation(
  order: OrderEntity,
  cancellingUserId: string
): Promise<CancellationResult> {
  const db = admin.firestore();
  const config = await getAppConfig();

  const hasBothParticipants = Boolean(order.buyerId && order.sellerId);
  const isActiveSinglePersonOrder =
    order.status === OrderStatus.ACTIVE && !hasBothParticipants;
  const isPaidTwoPersonOrder =
    order.status === OrderStatus.PAID && hasBothParticipants;

  if (isActiveSinglePersonOrder) {
    return await processSinglePersonCancellation(
      order,
      cancellingUserId,
      config,
      db
    );
  } else if (isPaidTwoPersonOrder) {
    return await processTwoPersonCancellation(
      order,
      cancellingUserId,
      config,
      db
    );
  } else {
    throw new Error(
      `Order ${order.id} is not in a valid state for cancellation`
    );
  }
}

async function processSinglePersonCancellation(
  order: OrderEntity,
  cancellingUserId: string,
  config: any,
  db: admin.firestore.Firestore
): Promise<CancellationResult> {
  const buyerLockPercentage = config?.buyer_lock_percentage as number;
  const sellerLockPercentage = config?.seller_lock_percentage as number;

  // Calculate locked amounts to unlock
  const buyerLockedAmount = safeMultiply(order.price, buyerLockPercentage);
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);

  // Unlock funds for the cancelling user
  if (order.buyerId === cancellingUserId) {
    await unlockFunds(order.buyerId, buyerLockedAmount);
  } else if (order.sellerId === cancellingUserId) {
    await unlockFunds(order.sellerId, sellerLockedAmount);
  }

  // Apply fixed cancellation fee if configured
  const feeApplied = await applyFixedCancelOrderFee(cancellingUserId);

  // Update order status to cancelled
  await db.collection("orders").doc(order.id).update({
    status: OrderStatus.CANCELLED,
    updatedAt: admin.firestore.FieldValue.serverTimestamp(),
  });

  const feeMessage =
    feeApplied > 0
      ? ` A cancellation fee of ${feeApplied} TON was applied.`
      : "";

  return {
    success: true,
    message: `Order cancelled successfully. Locked funds have been released.${feeMessage}`,
    feeApplied,
    feeType: "fixed",
  };
}

async function processTwoPersonCancellation(
  order: OrderEntity,
  cancellingUserId: string,
  config: any,
  db: admin.firestore.Firestore
): Promise<CancellationResult> {
  const cancelFeePercentage = config?.cancel_order_fee as number;
  const sellerLockPercentage = config?.seller_lock_percentage as number;

  // Calculate amounts
  const sellerLockedAmount = safeMultiply(order.price, sellerLockPercentage);
  const marketplaceFee = safeDivide(
    safeMultiply(order.price, cancelFeePercentage),
    BPS_DIVISOR
  );
  const compensationAmount = safeSubtract(order.price, marketplaceFee);

  // Determine who is cancelling and apply appropriate logic
  if (cancellingUserId === order.sellerId) {
    // Seller cancels: seller loses locked amount, buyer gets compensation
    await spendLockedFunds(order.sellerId, sellerLockedAmount);
    await updateUserBalance(order.buyerId!, compensationAmount, 0);
  } else if (cancellingUserId === order.buyerId) {
    // Buyer cancels: buyer loses their payment, seller gets compensation
    await spendLockedFunds(order.buyerId, order.price);
    await updateUserBalance(order.sellerId!, compensationAmount, 0);
    // Also unlock seller's locked amount
    await unlockFunds(order.sellerId!, sellerLockedAmount);
  }

  // Apply marketplace fee
  if (marketplaceFee > 0) {
    await applyFeeToMarketplaceRevenue(marketplaceFee, "cancel_order_penalty");
  }

  // Delete the order (same as expired orders)
  await db.collection("orders").doc(order.id).delete();

  const cancellerRole =
    cancellingUserId === order.sellerId ? UserRole.SELLER : UserRole.BUYER;
  const compensatedRole =
    cancellingUserId === order.sellerId ? UserRole.BUYER : UserRole.SELLER;

  return {
    success: true,
    message: `Order cancelled by ${cancellerRole}. ${compensatedRole} received ${compensationAmount} TON compensation. Marketplace fee: ${marketplaceFee} TON.`,
    feeApplied: marketplaceFee,
    feeType: "dynamic",
  };
}

export function validateCancellationPermission(
  order: OrderEntity,
  userId: string
) {
  if (order.buyerId !== userId && order.sellerId !== userId) {
    throw new Error(
      "You can only cancel orders where you are the buyer or seller."
    );
  }

  if (order.status === OrderStatus.FULFILLED) {
    throw new Error("Cannot cancel a fulfilled order.");
  }

  if (order.status === OrderStatus.CANCELLED) {
    throw new Error("Order is already cancelled.");
  }

  if (order.status === OrderStatus.GIFT_SENT_TO_RELAYER) {
    throw new Error(
      "Cannot cancel an order where the gift has already been sent to relayer."
    );
  }
}
