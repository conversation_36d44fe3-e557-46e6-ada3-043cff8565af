import * as admin from "firebase-admin";
import { OrderEntity, OrderStatus, UserType } from "../types";
import { lockFunds } from "./balance-service";
import { getNextCounterValue } from "./counter-service";
import { validateOrderCreation } from "./order-validation-service";

export interface OrderCreationParams {
  userId: string;
  collectionId: string;
  price: number;
  owned_gift_id?: string;
  userType: UserType;
  secondaryMarketPrice: number | null;
}

export interface OrderCreationResult {
  success: boolean;
  orderId: string;
  message: string;
  lockedAmount: number;
  lockPercentage: number;
}

export async function createOrder(
  db: admin.firestore.Firestore,
  params: OrderCreationParams
): Promise<OrderCreationResult> {
  const { userId, collectionId, price, owned_gift_id, userType } = params;

  const validation = await validateOrderCreation(
    db,
    userId,
    collectionId,
    price,
    userType
  );

  await lockFunds(userId, validation.lockedAmount);

  const orderNumber = await getNextCounterValue("order_number");

  const orderData: Omit<OrderEntity, "id"> = {
    number: orderNumber,
    collectionId,
    price,
    status: OrderStatus.ACTIVE,
    owned_gift_id,
    createdAt: admin.firestore.FieldValue.serverTimestamp() as any,
    updatedAt: admin.firestore.FieldValue.serverTimestamp() as any,
  };

  if (userType === UserType.BUYER) {
    orderData.buyerId = userId;
  } else {
    orderData.sellerId = userId;
  }

  // Create the order
  const orderRef = await db.collection("orders").add(orderData);

  return {
    success: true,
    orderId: orderRef.id,
    message: `Order created successfully with ${
      validation.lockedAmount
    } TON locked (${validation.lockPercentage * 100}% of ${price} TON order)`,
    lockedAmount: validation.lockedAmount,
    lockPercentage: validation.lockPercentage,
  };
}
